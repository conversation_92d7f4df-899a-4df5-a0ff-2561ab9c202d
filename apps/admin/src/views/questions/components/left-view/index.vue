<script setup lang="ts">
import TitleForm from './components/title-form.vue'
import TextForm from './components/text-form.vue'
import ChapterForm from './components/chapter-form.vue'
import DocumentForm from './components/document-form.vue'
import { useAuthStore } from '@/store/modules/auth'
import { getAgentModelInfo, getDifficulty, getLearningLevel, getQuestionTypes } from '@/service/api'

defineOptions({
  name: 'LeftView',
})

// 获取认证store
const authStore = useAuthStore()
const { userInfo } = authStore
// 创建方式选项
const createMethods = [
  { key: 'title', label: '知识点出题 ', active: true, mode: 1 },
  { key: 'text', label: '文本出题', active: false, mode: 2 },
  { key: 'chapter', label: '附件出题', active: false, mode: 3 },
  { key: 'document', label: '章节出题', active: false, mode: 4 },
]

// AI模型选择选项
const modelOptions = ref([] as QuestionsApi.AgentModelInfoResponse[])

const activeCreateMethod = ref('title')

// 表单数据
const formModel = reactive<QuestionsApi.CreateQuestionRequest>({


  AdditionalRequirements: '', // 补充内容（出题要求）
  AIModeId: '', // AI模型ID
  ChapterName: '',
  DifficultyLevelName: '', // 难度等级名称
  FileUrl: '', // 文件URL
  Grade: 1, // 年级
  KnowledgePointIds: [], // 知识点ID列表
  Mode: 1, // 出题模式
  QuestionCount: 5, // 出题数量
  QuestionDirectionName: '', // 出题方向名称
  QuestionTypeIds: [], // 题型ID列表
  TextContent: '', // 文本内容
  
})

// 班级选项
const classOptions = computed(() => {
  return (userInfo.classList || []).map(classItem => ({
    label: classItem.ClassName,
    value: classItem.ClassId,
  }))
})

// 更新表单数据
function updateFormData(data: any) {
  Object.assign(formModel.value, data)
}
// 处理模型选择
function handleModelSelect(key: string) {
  formModel.value.Mode = key
}
// 处理创建方式切换
function switchCreateMethod(method: string) {
  activeCreateMethod.value = method
}
function generateLesson() {

}
const questionTypeOptions = ref([] as QuestionsApi.GetQuestionTypesResponse[])
const difficultyOptions = ref([] as QuestionsApi.GetDifficultyResponse[])
const learningLevelOptions = ref([] as QuestionsApi.GetLearningLevelResponse[])
function initData() {
  getAgentModelInfo().then((res) => {
    if (res.data) {
      modelOptions.value = res.data
      formModel.AIModeId = res.data[0].Id
    }
  })
  getQuestionTypes().then((res) => {
    if (res.data) {
      questionTypeOptions.value = res.data
    }
  })
  getDifficulty({
    grade: 1,
    year: '2023',
  }).then((res) => {
    if (res.data) {
      difficultyOptions.value = res.data
    }
  })
  getLearningLevel({
    grade: 1,
    year: '2023',
  }).then((res) => {
    if (res.data) {
      learningLevelOptions.value = res.data
    }
  })
}
// 年级选择选项
const gradeOptions = ref([
  { label: '一年级', value: '1' },
  { label: '二年级', value: '2' },
  { label: '三年级', value: '3' },
  { label: '四年级', value: '4' },
  { label: '五年级', value: '5' },
  { label: '六年级', value: '6' },
])
onMounted(() => {
  initData()
})
</script>

<template>
  <div class="h-full bg-#F5FBFE">
    <div class="flex-shrink-0">
      <!-- 标题栏 -->
      <div class="mb-16px flex items-center justify-between">
        <div class="flex items-center gap-8px">
          <div class="h-38px w-38px flex items-center justify-center rounded-8px from-blue-500 to-purple-500 bg-gradient-to-r">
            <SvgIcon icon="mdi:robot-outline" class="text-20px text-white" />
          </div>
          <span class="from-blue-500 to-purple-500 bg-gradient-to-r bg-clip-text text-22px text-transparent font-600">
            AI智能命题
          </span>
        </div>

        <!-- AI模型选择下拉框 -->
        <NDropdown
          trigger="click"
          key-field="Id"
          label-field="ModelName"
          :options="modelOptions"
          :show-arrow="true"
          @select="handleModelSelect"
        >
          <NButton quaternary class="flex items-center gap-6px rounded-8px bg-blue-100 px-12px py-8px hover:bg-blue-200">
            <SvgIcon icon="mdi:brain" class="mr-4px text-16px text-blue-600" />
            <span class="text-14px text-blue-700">{{ formModel.Mode ? modelOptions.find(m => m.Id === formModel.AIModeId)?.ModelName : '选择模型' }}</span>
            <SvgIcon icon="mdi:chevron-down" class="text-12px text-blue-600" />
          </NButton>
        </NDropdown>
      </div>

      <!-- 创建方式选项卡 -->
      <div class="mb-16px flex justify-between rounded-8px bg-blue-100 p-4px">
        <div
          v-for="method in createMethods"
          :key="method.key"
          class="cursor-pointer rounded-6px px-16px py-8px text-14px font-500 transition-all duration-200"
          :class="[
            activeCreateMethod === method.key
              ? 'bg-white text-blue-600 shadow-sm '
              : ' hover:text-blue-600',
          ]"
          @click="switchCreateMethod(method.key)"
        >
          {{ method.label }}
        </div>
      </div>
    </div>
    <!-- 内容区域 -->
    <div class="min-h-0 flex-shrink-1">
      <NScrollbar class="h-full">
        <!-- 表单区域 -->
        <NForm
          :model="formModel"
          label-placement="top"
          size="medium"
        >
          <!-- 根据创建方式渲染不同的表单组件 -->
          <TitleForm
            v-if="activeCreateMethod === 'title'"
            :model-value="{
              selectedKnowledgePoints: formModel.selectedKnowledgePoints,
              additionalRequirements: formModel.additionalRequirements,
            }"
            @update:model-value="updateFormData"
          />

          <TextForm
            v-else-if="activeCreateMethod === 'text'"
            :model-value="{
              textContent: formModel.textContent,
              questionType: formModel.questionType,
              difficulty: formModel.difficulty,
              questionCount: formModel.questionCount,
              otherRequirements: formModel.otherRequirements,
            }"
            @update:model-value="updateFormData"
          />

          <ChapterForm
            v-else-if="activeCreateMethod === 'chapter'"
            :model-value="{
              attachments: formModel.attachments,
              questionType: formModel.questionType,
              difficulty: formModel.difficulty,
              questionCount: formModel.questionCount,
              extractMethod: formModel.extractMethod,
              otherRequirements: formModel.otherRequirements,
            }"
            @update:model-value="updateFormData"
          />

          <DocumentForm
            v-else-if="activeCreateMethod === 'document'"
            :model-value="{
              subject: formModel.subject,
              chapter: formModel.chapter,
              section: formModel.section,
              questionType: formModel.questionType,
              difficulty: formModel.difficulty,
              questionCount: formModel.questionCount,
              knowledgePoints: formModel.knowledgePoints,
              otherRequirements: formModel.otherRequirements,
            }"
            @update:model-value="updateFormData"
          />
        </NForm>
      </Nscrollbar>
    </div>

    <!-- 生成按钮区域 -->
    <div class="mt-24px flex-shrink-0 rounded-8px bg-blue-50 p-16px text-center">
      <NForm
        ref="formRef"
        inline
        :label-width="80"
        :model="formModel"
      >
        <NFormItem>
          <NSelect v-model:value="formModel.QuestionTypeIds" :options="questionTypeOptions" />
        </NFormItem>
        <NFormItem>
          <NSelect v-model:value="formModel.DifficultyLevelName" :options="difficultyOptions" />
        </NFormItem>
        <NFormItem>
          <NSelect v-model:value="formModel.QuestionDirectionName" :options="learningLevelOptions" />
        </NFormItem>
        <NFormItem>
          <NInput v-model:value="formModel.QuestionCount" placeholder="请输入出题梳理" />
        </NFormItem>
      </NForm>

      <NButton
        type="primary"
        size="large"
        class="mb-12px from-blue-500 to-purple-500 bg-gradient-to-r px-24px py-12px"
        @click="generateLesson"
      >
        <template #icon>
          <SvgIcon icon="mdi:magic-staff" />
        </template>
        一键生成教案
      </NButton>
      <p class="text-12px text-gray-500">
        内容由AI生成，仅供参考。
      </p>
    </div>
  </div>
</template>

<style scoped lang="scss">

</style>
