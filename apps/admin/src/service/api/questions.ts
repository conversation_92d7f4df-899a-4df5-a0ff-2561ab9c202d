import { appApiRequest, request } from '@/service/request'

/**
 * 获取智能体模型信息
 * @returns 返回智能体模型信息
 */
export function getAgentModelInfo() {
  return request<QuestionsApi.AgentModelInfoResponse[]>({
    url: 'AgentCommon/AgentCommon/GetAgentModelInfo',
    method: 'POST',
  })
}
/**
 * 获取难度接口
 * @param data 请求参数
 *   @property {number} grade - 年级
 *   @property {string} year - 学年
 * @returns 返回难度信息
 */
export function getDifficulty(data: { grade: number, year: string }) {
  return appApiRequest<QuestionsApi.GetDifficultyResponse[]>({
    url: 'Question/Question/GetDifficulty',
    method: 'GET',
    params: data,
  })
}

/**
 * 获取学习水平接口
 * @param data 请求参数
 *   @property {number} grade - 年级
 *   @property {string} year - 学年
 * @returns 返回学习等级信息
 */
export function getLearningLevel(data: { grade: number, year: string }) {
  return appApiRequest<QuestionsApi.GetLearningLevelResponse[]>({
    url: 'Question/Question/GetLearninglevel',
    method: 'GET',
    params: data,
  })
}

/**
 * 获取题目类型接口
 * @returns 返回题目类型信息
 */
export function getQuestionTypes() {
  return appApiRequest<QuestionsApi.GetQuestionTypesResponse[]>({
    url: '/AgentIntelligentQuestion/AgentIntelligentQuestion/GetQuestionTypes',
    method: 'GET',
  })
}
